<!--
 * @Description: 
 * @Author: gao<PERSON><PERSON>
 * @Date: 2021-05-13 09:24:52
 * @LastEditors: othniel <EMAIL>
 * @LastEditTime: 2025-05-16 17:53:06
-->
<template>
  <div>
    <!-- <Com :data="{ type: 'primary', showName: '你好' }"></Com> -->

    <!-- <Com :dataItem="dataItem"></Com> -->
    <Com></Com>
  </div>
</template>

<script>
// import { encodeDes } from "../../utils/utils";
// import Com from "../../views/boot-console/attribute/tableOperationAdd.vue";
// import Com from "../SMC_PC2/IndexGeneralView/index.vue";
// import Com from "../SMC_PC2/IndexComparison/index.vue";
// import Com from "../SMC_PC2/Card/CardInfo/cardDetailInfo.vue";

// import Com from "../SMC_PC2/IndexGeneralView/index.vue";
// import Com from "../SMC_PC2/Card/DetectionInfo/info.vue"; //要单独抽离的组件

// import Com from "../../views/boot-console/index-card/tableLineOperationEdit.vue";

// import Com from "../SMC_PC/IndexComparison/index.vue";
// import Com from "../SMC_PC/CardListInHiData/index.vue";
// import Com from "../boot-console/gate-config/tableOperationPerson.vue";
// import Com from "../boot-console/target/tableLineOperationEdit.vue";
// import Com from "../cloudSetting/indexTargetFillIn/tableCell.vue";

import Com from "../boot-console/org-manage/index.vue";

// import Com from "../boot-console/role/tableLineOperationEdit.vue";
// import Com from "../boot-console/base-setting/tableOperationImport.vue";
// import Com from "../boot-console/front-fill/tableLineOperationAdd.vue";
// import Com from "../boot-console/front-fill/tableLineOperationEdit.vue";
// import Com from "../boot-console/registry/tableOperationAdd.vue";
// import Com from "../boot-console/registry/tableOperationAdd.vue";
// import Com from "../boot-console/attribute/tableOperationAdd.vue";
// import Com from "../boot-console/attribute/tableLineOperationEdit.vue";
// import Com from "../boot-console/SXKJ-fillpage/tableLineOperationEdit.vue";
// import Com from "../boot-console/SXKJ-fillpage/tableOperationEdit1.vue"; //直通率闸口的责任人维护
// import Com from "../boot-console/SXKJ-fillpage/examineBtn.vue"

// import Com from "../DMSBIPageReview/index.vue";

// import Com from "../boot-console/early-warning/tableOperationAdd.vue";
// import Com from "../boot-console/self-report-push/tableOperationAdd.vue";
export default {
  name: "TestEntry",
  components: {
    Com,
  },
  data() {
    return {
      dataItem: {
        sign: "null",
        indexId: "ZBY00740",
        indexName: null,
        indexDt: "2025-02",
        fullCode: "HX/H/H0104/CM_TREE002281",
        indexFrequencyId: "M",
        org: "黄岛基地",
        orgId: "CM_TREE002281",
        businessSegments: "效率",
        businessSegmentsId: "SEG_83",
        cmimId:
          "ZBY00740-CM_TREE002281-M-0000-0000-0000-0000-0000-0000-0000-0000-0000-0000-0000",
        productAtt1Id: "null",
        productAtt2Id: null,
        productAtt3Id: null,
        productAtt4Id: null,
        productAtt5Id: null,
        productAtt6Id: null,
        productAtt7Id: null,
        // displayIndexName: "上线失效率",
        // wdInCardName: "电视主板",
        // indexType: "反向",
        // companyName: "智动精工",
      },
    };
  },
  mounted() {
    // console.log("encodeDes----->", encodeDes("wangxiaoxian", true, "dhrLogin"));
  },
};
</script>
